{"7.12.0": {"date": "2025-06-15", "changes": ["feat(changelog): implement automatic changelog popup on version update", "Store last seen script version in GM storage and compare on load.", "Fetch changelog data from JSON and display updates when a new version is detected.", "Enhance user experience with quick, non-intrusive update popups."]}, "7.11.0": {"date": "2025-05-25", "changes": ["feat(bookmarks): add flip button to toggle between related and bookmarked manga", "Add a new setting to enable a flip button that allows users to toggle between the original related manga and their bookmarked content.", "The button is only visible when 'Replace Related Manga with Bookmarks' is enabled."]}}